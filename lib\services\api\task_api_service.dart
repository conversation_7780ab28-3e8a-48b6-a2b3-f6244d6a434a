import 'package:flutter/foundation.dart';
import 'package:flutter_application_2/models/task_type_models.dart';
import 'package:flutter_application_2/models/task_priority_models.dart';
import '../../models/task_models.dart';
import 'api_service.dart';

/// خدمة API للمهام
class TaskApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المهام
  Future<List<Task>> getAllTasks() async {
    try {
      final response = await _apiService.get('/api/Tasks');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام: $e');
      rethrow;
    }
  }

  /// الحصول على مهمة بواسطة المعرف
  Future<Task?> getTaskById(int id) async {
    try {
      final response = await _apiService.get('/api/Tasks/$id');
      return _apiService.handleResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهمة: $e');
      return null;
    }
  }

  /// إنشاء مهمة جديدة
  Future<Task?> createTask(Task task) async {
    try {
      final response = await _apiService.post(
        '/api/Tasks',
        task.toJson(),
      );
      return _apiService.handleResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء المهمة: $e');
      rethrow;
    }
  }

  /// تحديث مهمة
  Future<Task?> updateTask(Task task) async {
    try {
      final response = await _apiService.put(
        '/api/Tasks/${task.id}',
        task.toJson(),
      );
      return _apiService.handleResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المهمة: $e');
      rethrow;
    }
  }

  /// حذف مهمة (حذف ناعم)
  Future<bool> deleteTask(int id) async {
    try {
      final response = await _apiService.delete('/api/Tasks/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المهمة: $e');
      return false;
    }
  }

  /// الحصول على المهام بحسب المكلف بها
  Future<List<Task>> getTasksByAssignee(int assigneeId, {Map<String, dynamic>? queryParams}) async {
    try {
      final response = await _apiService.get('/api/Tasks/assignee/$assigneeId', queryParams: queryParams);
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام المكلف $assigneeId: $e');
      rethrow;
    }
  }

  /// الحصول على المهام بحسب المنشئ
  Future<List<Task>> getTasksByCreator(int creatorId) async {
    try {
      final response = await _apiService.get('/api/Tasks/creator/$creatorId');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام المنشئ $creatorId: $e');
      rethrow;
    }
  }

  /// الحصول على المهام بحسب القسم
  Future<List<Task>> getTasksByDepartment(int departmentId) async {
    try {
      final response = await _apiService.get('/api/Tasks/department/$departmentId');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام القسم $departmentId: $e');
      rethrow;
    }
  }

  /// الحصول على المهام بحسب الحالة
  Future<List<Task>> getTasksByStatus(int statusId) async {
    try {
      final response = await _apiService.get('/api/Tasks/status/$statusId');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام الحالة $statusId: $e');
      rethrow;
    }
  }

  /// الحصول على المهام بحسب الأولوية
  Future<List<Task>> getTasksByPriority(int priorityId) async {
    try {
      final response = await _apiService.get('/api/Tasks/priority/$priorityId');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام الأولوية $priorityId: $e');
      rethrow;
    }
  }

  /// البحث في المهام
  Future<List<Task>> searchTasks(String query) async {
    try {
      final response = await _apiService.get('/api/Tasks/search', queryParams: {'q': query});
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث في المهام: $e');
      return [];
    }
  }

  /// الحصول على المهام المتأخرة
  Future<List<Task>> getOverdueTasks() async {
    try {
      final response = await _apiService.get('/api/Tasks/overdue');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام المتأخرة: $e');
      return [];
    }
  }

  /// الحصول على المهام المكتملة
  Future<List<Task>> getCompletedTasks() async {
    try {
      final response = await _apiService.get('/api/Tasks/completed');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام المكتملة: $e');
      return [];
    }
  }

  /// تحديث حالة المهمة
  Future<bool> updateTaskStatus(int taskId, int statusId) async {
    try {
      final response = await _apiService.put(
        '/api/Tasks/$taskId/status',
        {'status': statusId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة المهمة: $e');
      return false;
    }
  }

  /// تحديث نسبة إنجاز المهمة
  Future<bool> updateTaskProgress(int taskId, int percentage) async {
    try {
      final response = await _apiService.put(
        '/api/Tasks/$taskId/progress',
        {'completionPercentage': percentage},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث نسبة إنجاز المهمة: $e');
      return false;
    }
  }



  /// الحصول على جميع حالات المهام
  Future<List<TaskStatus>> getTaskStatuses() async {
    try {
      final response = await _apiService.get('/api/TaskStatus');
      return _apiService.handleListResponse<TaskStatus>(
        response,
        (json) => TaskStatus.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على حالات المهام: $e');
      rethrow;
    }
  }

  /// الحصول على جميع أولويات المهام
  Future<List<TaskPriority>> getTaskPriorities() async {
    try {
      final response = await _apiService.get('/api/TaskPriority');
      return _apiService.handleListResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أولويات المهام: $e');
      rethrow;
    }
  }

  /// الحصول على جميع أنواع المهام
  Future<List<TaskType>> getTaskTypes() async {
    try {
      final response = await _apiService.get('/api/TaskTypes');
      return _apiService.handleListResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أنواع المهام: $e');
      rethrow;
    }
  }

  /// إنشاء حالة مهمة جديدة
  Future<TaskStatus?> createTaskStatus(TaskStatus status) async {
    try {
      final response = await _apiService.post(
        '/api/TaskStatus',
        status.toJson(),
      );
      return _apiService.handleResponse<TaskStatus>(
        response,
        (json) => TaskStatus.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء حالة المهمة: $e');
      rethrow;
    }
  }

  /// إنشاء أولوية مهمة جديدة
  Future<TaskPriority?> createTaskPriority(TaskPriority priority) async {
    try {
      final response = await _apiService.post(
        '/api/TaskPriority',
        priority.toJson(),
      );
      return _apiService.handleResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء أولوية المهمة: $e');
      rethrow;
    }
  }

  /// إنشاء نوع مهمة جديد
  Future<TaskType?> createTaskType(TaskType type) async {
    try {
      final response = await _apiService.post(
        '/api/TaskTypes',
        type.toJson(),
      );
      return _apiService.handleResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء نوع المهمة: $e');
      rethrow;
    }
  }

  /// تحديث حالة مهمة (نموذج)
  Future<TaskStatus?> updateTaskStatusModel(TaskStatus status) async {
    try {
      final response = await _apiService.put(
        '/api/TaskStatus/${status.id}',
        status.toJson(),
      );
      return _apiService.handleResponse<TaskStatus>(
        response,
        (json) => TaskStatus.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث حالة المهمة: $e');
      rethrow;
    }
  }

  /// تحديث أولوية مهمة
  Future<TaskPriority?> updateTaskPriority(TaskPriority priority) async {
    try {
      final response = await _apiService.put(
        '/api/TaskPriority/${priority.id}',
        priority.toJson(),
      );
      return _apiService.handleResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث أولوية المهمة: $e');
      rethrow;
    }
  }

  /// تحديث نوع مهمة
  Future<TaskType?> updateTaskType(TaskType type) async {
    try {
      final response = await _apiService.put(
        '/api/TaskTypes/${type.id}',
        type.toJson(),
      );
      return _apiService.handleResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث نوع المهمة: $e');
      rethrow;
    }
  }

  /// حذف حالة مهمة
  Future<bool> deleteTaskStatus(int id) async {
    try {
      final response = await _apiService.delete('/api/TaskStatuses/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف حالة المهمة: $e');
      return false;
    }
  }

  /// حذف أولوية مهمة
  Future<bool> deleteTaskPriority(int id) async {
    try {
      final response = await _apiService.delete('/api/TaskPriorities/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف أولوية المهمة: $e');
      return false;
    }
  }

  /// حذف نوع مهمة
  Future<bool> deleteTaskType(int id) async {
    try {
      final response = await _apiService.delete('/api/TaskTypes/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف نوع المهمة: $e');
      return false;
    }
  }
}
